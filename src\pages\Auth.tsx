
  import React, { useState } from 'react';
  import { useAuth } from '@/contexts/AuthContext';
  import { Navigate } from 'react-router-dom';
  import { Button } from '@/components/ui/button';
  import { Input } from '@/components/ui/input';
  import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
  import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
  import { Label } from '@/components/ui/label';
  import { Eye, EyeOff, Mail, Phone } from 'lucide-react';

  const Auth: React.FC = () => {
    const { user, signIn, signInWithPhone, resetPassword } = useAuth();
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('signin');
    const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email'); // Email is set as default
    const [showPassword, setShowPassword] = useState(false);

    // Redirect if user is already authenticated
    if (user) {
      return <Navigate to="/dashboard" replace />;
    }

    const handleSignIn = async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);

      if (loginMethod === 'email') {
        await signIn(email, password);
      } else {
        await signInWithPhone(phone, password);
      }

      setLoading(false);
    };



    const handleResetPassword = async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);
      await resetPassword(email);
      setLoading(false);
    };

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center items-center mb-4">
              <img alt="Investment Portal" src="/lovable-uploads/ab651bb9-5504-40da-ab30-1bd5fd7fdc5a.png" className="h-12 object-cover" />
              <img alt="Investment Portal" src="/lovable-uploads/e26f8b82-a2a2-46cc-a26e-fcf66ed47997.png" className="h-8 object-cover" />
            </div>
            <CardTitle className="text-2xl">Welcome Back</CardTitle>
            <CardDescription>Access your investment management system</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-1">
                <TabsTrigger value="signin">Sign In</TabsTrigger>
              </TabsList>

              <TabsContent value="signin">
                <div className="mb-6">
                  <Label className="block mb-2 text-sm font-medium text-gray-700">Login Method</Label>
                  <div className="flex gap-2 bg-gray-100 rounded-lg p-1 border border-gray-200">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setLoginMethod('email')}
                      className={`flex-1 rounded-md transition-colors duration-150 ${loginMethod === 'email' ? 'bg-white shadow text-primary font-semibold' : 'bg-transparent text-gray-500 hover:bg-gray-200'}`}
                      tabIndex={0}
                      aria-pressed={loginMethod === 'email'}
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Email
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setLoginMethod('phone')}
                      className={`flex-1 rounded-md transition-colors duration-150 ${loginMethod === 'phone' ? 'bg-white shadow text-primary font-semibold' : 'bg-transparent text-gray-500 hover:bg-gray-200'}`}
                      tabIndex={0}
                      aria-pressed={loginMethod === 'phone'}
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Phone
                    </Button>
                  </div>
                </div>

                <form onSubmit={handleSignIn} className="space-y-4">
                  {loginMethod === 'email' ? (
                    <div>
                      <Label htmlFor="signin-email">Email</Label>
                      <Input
                        id="signin-email"
                        type="email"
                        value={email}
                        autoComplete="email"
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                  ) : (
                    <div>
                      <Label htmlFor="signin-phone">Phone Number</Label>
                      <Input
                        id="signin-phone"
                        type="tel"
                        value={phone}
                        autoComplete="tel"
                        placeholder="+1234567890"
                        onChange={(e) => setPhone(e.target.value)}
                        required
                      />
                    </div>
                  )}

                  <div>
                    <Label htmlFor="signin-password">Password</Label>
                    <div className="relative">
                      <Input
                        id="signin-password"
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        autoComplete="current-password"
                        onChange={(e) => setPassword(e.target.value)}
                        required
                      />
                      <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <button
                      type="button"
                      className="text-sm text-primary hover:underline focus:outline-none"
                      onClick={() => setActiveTab('forgot')}
                    >
                      Forgot password?
                    </button>
                  </div>
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? 'Signing In...' : 'Sign In'}
                  </Button>
                </form>
              </TabsContent>

              {activeTab === 'forgot' && (
                <div className="mt-4">
                  <form onSubmit={handleResetPassword} className="space-y-4">
                    <div>
                      <Label htmlFor="reset-email">Enter your email to reset password</Label>
                      <Input
                        id="reset-email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button type="submit" className="flex-1" disabled={loading}>
                        {loading ? 'Sending...' : 'Send Reset Link'}
                      </Button>
                      <Button type="button" variant="outline" className="flex-1" onClick={() => setActiveTab('signin')}>
                        Back to Sign In
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </Tabs>
          </CardContent>
        </Card>
      </div>
    );
  };

  export default Auth;
